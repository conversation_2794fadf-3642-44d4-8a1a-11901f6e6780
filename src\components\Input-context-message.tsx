interface InputContextMessageProps {
  content: string
}

export function InputContextMessage({ content }: InputContextMessageProps) {
  return (
    <div className="flex w-full justify-end">
      <div className="w-fit rounded-sm border border-solid border-base-accent-600 bg-base-primary-100 p-3 md:max-w-[50%]">
        <p className="overflow-hidden whitespace-pre-wrap break-words text-base-gray-900 ts-paragraph-xs">
          {content}
        </p>
      </div>
    </div>
  )
}
