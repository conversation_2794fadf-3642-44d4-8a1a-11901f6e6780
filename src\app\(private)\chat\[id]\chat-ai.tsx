'use client'

import { But<PERSON>, useAlert } from '@ads/components-react'
import { useParams } from 'next/navigation'
import { useState } from 'react'
import { useMutation, useQuery } from 'react-query'

import NotFound from '@/app/not-found'
import { CardCourse } from '@/components/card-course'
import { InputContextMessage } from '@/components/Input-context-message'
import { ChatPageSkeleton } from '@/components/loaders/skeletons/chat-page'
import { ResponseAI } from '@/components/response-ai'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useAuth } from '@/contexts/AuthContext'
import { CreateStudyPlanMutationVariables } from '@/graphql/generated/graphql'
import { getChatMessageAI } from '@/http/chat-ai/get-chat-ai-message'
import { createNewStudyPlan } from '@/http/study-plan'

export function ChatAI() {
  const { alert } = useAlert()
  const { user } = useAuth()
  const params = useParams()
  const chatId = params.id

  const [selectedIds, setSelectedIds] = useState<string[]>([])

  const { data, isLoading } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => getChatMessageAI(chatId as string),
  })

  function handleCardChange(id: string, checked: boolean) {
    setSelectedIds((prev) => {
      const set = new Set(prev)
      if (checked) {
        set.add(id)
      } else {
        set.delete(id)
      }
      return Array.from(set)
    })
  }

  const { mutate: newStudyPlanMutation, isLoading: isCreatingStudyPlan } =
    useMutation({
      mutationFn: (data: CreateStudyPlanMutationVariables) =>
        createNewStudyPlan(data),
      onSuccess: (_, data) => {
        alert({
          title: 'Trilha de aprendizado criada com sucesso!',
          description: `A trilha ${data.name} foi criada com sucesso.`,
          alertType: 'success',
        })
      },
      onError: () => {
        alert({
          title: 'Erro ao criar trilha de aprendizado',
          description:
            'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
          alertType: 'danger',
        })
      },
    })

  if (isLoading) {
    return <ChatPageSkeleton />
  }

  if (!data) {
    return <NotFound />
  }

  const aiResponse = data?.[0].ai_response
  const isInvalidCourses = !data?.[0].ai_response.courses

  const handleCreateStudyPlan = () => {
    newStudyPlanMutation({
      name: aiResponse?.title as string,
      description: aiResponse?.description as string,
      courses: selectedIds.map((id, index) => ({
        course_id: Number(id),
        order: index,
      })),
      company_id: user?.metadata.company_id as number,
      squad_ids: [],
      user_ids: [],
      is_pdi: false,
      is_for_all_users: false,
      is_for_all_squads: false,
      is_for_all_courses: false,
    })
  }

  return (
    <div className="flex h-[calc(100vh-150px)] flex-col overflow-hidden md:h-[calc(100vh-192px)]">
      <div className="min-h-0 flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="mx-auto w-full max-w-[58rem] space-y-4 p-4 md:px-4">
            <InputContextMessage content={data?.[0]?.user_input ?? ''} />

            <ResponseAI
              content={
                aiResponse.content
                  ? aiResponse.content
                  : `${aiResponse.description} Confira os ${aiResponse.count} sugeridos `
              }
            />

            <div className="space-y-4">
              {aiResponse?.courses?.map((course, index) => (
                <div key={course.id}>
                  <CardCourse
                    id={String(course.id)}
                    index={index + 1}
                    title={course.title}
                    imageUrl={course.image_url}
                    imageAlt={`Imagem do curso ${course.title}`}
                    justification={course.justification}
                    checked={selectedIds.includes(String(course.id))}
                    onChange={handleCardChange}
                  />
                </div>
              ))}
              {!isInvalidCourses && (
                <div className="flex flex-col gap-4">
                  <span className="text-ctx-content-base ts-paragraph-xs">
                    Selecione pelo menos 1 curso sugerido e clique em uma opção
                    abaixo:
                  </span>
                  <Button
                    onClick={handleCreateStudyPlan}
                    hierarchy="secondary"
                    disabled={selectedIds.length === 0 || isCreatingStudyPlan}
                  >
                    Criar trilha com os cursos selecionados
                  </Button>
                </div>
              )}
            </div>
          </div>
        </ScrollArea>
      </div>
      {/* TODO: Trazer Input de volta quando tiver a regra completamente definida */}
      {/* <div className="flex-shrink-0">
        <Separator className="my-2 md:my-4" />
        <div className="mx-auto w-full max-w-[50rem] px-4 pb-4">
          <InputChat placeholder="Descreva seu desafio, dúvida ou envie um arquivo." />
        </div>
      </div> */}
    </div>
  )
}
