'use client'
/* eslint-disable @typescript-eslint/no-unused-vars */

import { PasswordField, TextField, TextFieldProps } from '@ads/components-react'
import { forwardRef } from 'react'
import { HiOutlinePencilSquare } from 'react-icons/hi2'
import { PiWarningCircle } from 'react-icons/pi'

interface InputFormProps extends TextFieldProps {
  textError?: string
  isAutoComplete?: boolean
  size?: 'sm' | 'md' | 'lg'
  onColor?: boolean
}

function InputFormWithRef(
  {
    id,
    label,
    type = 'text',
    size = 'lg',
    textError,
    leadingIcon,
    isAutoComplete = false,
    onColor = true,
    ...rest
  }: InputFormProps,
  ref: React.ForwardedRef<HTMLInputElement>
) {
  const hasError = Boolean(textError)
  const isPassword = type === 'password'

  const handleTrailingIcon = () => {
    if (hasError) {
      return PiWarningCircle
    }

    if (isAutoComplete && !hasError) {
      return HiOutlinePencilSquare
    }

    return undefined
  }

  if (!isPassword) {
    return (
      <TextField
        ref={ref}
        size={size}
        type={type}
        onColor={onColor}
        fullWidth
        label={label}
        leadingIcon={leadingIcon}
        hasError={hasError}
        helper={textError}
        trailingIcon={handleTrailingIcon()}
        {...rest}
      />
    )
  } else {
    return (
      <PasswordField
        ref={ref}
        ariaHidePassword="Mostrar senha"
        ariaShowPassword="Esconder senha"
        size={size}
        onColor={onColor}
        fullWidth
        label={label}
        hasError={hasError}
        helper={textError}
        {...rest}
      />
    )
  }
}

const InputForm = forwardRef(InputFormWithRef)

export default InputForm
